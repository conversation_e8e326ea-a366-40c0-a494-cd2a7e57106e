import { Card, CardContent, CardDescription, CardHeader, CardTitle } from 'src/lib/components/ui/card';
import { FormNavigation } from 'src/shared/form-navigation';
import { Button } from 'src/lib/components/ui/button';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { ParticipantForm } from 'src/pages/components/participant-form';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from 'src/lib/components/ui/accordion';
import { TotalPrice } from 'src/pages/components/total-price';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from 'src/lib/components/ui/dialog';
import { useFormContext } from 'src/context/form-context';
import { useTranslation } from 'react-i18next';

export function ParticipantsPage() {
  const { t } = useTranslation();
  const { form, validateParticipant, scrollToError } = useFormContext();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [participantToDelete, setParticipantToDelete] = useState<number | null>(null);
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const addParticipant = useFormProgressStore(state => state.addParticipant);
  const removeParticipant = useFormProgressStore(state => state.removeParticipant);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const setForceErrorMessages = useFormProgressStore(state => state.setForceErrorMessages);
  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const [openParticipants, setOpenParticipants] = useState<string[]>(
    Array.from({ length: registrationEntries.length }, (_, i) => String(i))
  );

  useEffect(() => {
    validateParticipant(registrationEntries.length - 1);
  }, [validateParticipant, registrationEntries.length]);

  const numberOfParticipants = useMemo(() => {
    return registrationEntries.length;
  }, [registrationEntries]);

  const handleAddParticipant = () => {
    addParticipant(form);
    setOpenParticipants(prev => prev.concat(String(numberOfParticipants)));
    setTimeout(() => {
      const element = document.getElementById(`participant-${numberOfParticipants}`);
      if (!element) return;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - 70;
      window.scrollTo({ behavior: 'smooth', top: offsetPosition });
    }, 100);
  };

  const getParticipantLabel = (index: number) => {
    const participant = registrationEntries[index];
    if (participant.fields.firstName && participant.fields.lastName) {
      return `${participant.fields.firstName} ${participant.fields.lastName}`;
    } else if (participant.fields.firstName) {
      return String(participant.fields.firstName);
    }
    return String(index + 1);
  };

  const handleRemoveParticipant = (index: number, e: any) => {
    e.stopPropagation(); // Prevent accordion from toggling
    if (numberOfParticipants > 1) {
      setParticipantToDelete(index);
      setDeleteDialogOpen(true);
    }
  };

  const confirmDelete = () => {
    if (participantToDelete !== null && numberOfParticipants > 1) {
      removeParticipant(participantToDelete);
      setDeleteDialogOpen(false);
      setParticipantToDelete(null);
    }
  };

  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setParticipantToDelete(null);
  };

  const onGoNext = () => {
    setForceErrorMessages(true);
    if (invalidFields.length > 0) {
      scrollToError();
      return false;
    }

    return true;
  };

  const disableNextButton = invalidFields.length > 0 && forceErrorMessages;

  const participantIndices = Array.from(Array(numberOfParticipants).keys());

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t('page.participants.title')}</CardTitle>
        <CardDescription>{t('page.participants.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {numberOfParticipants > 1 ? (
          <Accordion type="multiple" className="w-full" value={openParticipants} onValueChange={setOpenParticipants}>
            {participantIndices.map(index => (
              <AccordionItem key={index} id={`participant-${index}`} value={String(index)}>
                <AccordionTrigger className="group">
                  <div className="flex grow items-center justify-between">
                    <span>{getParticipantLabel(index)}</span>
                    {numberOfParticipants > 1 && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="ml-2 h-6 w-6 opacity-70 hover:opacity-100 hover:bg-destructive/10 hover:text-destructive"
                        onClick={e => handleRemoveParticipant(index, e)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <ParticipantForm participantIndex={index} />
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <ParticipantForm participantIndex={0} />
        )}

        <div className="flex justify-between">
          <TotalPrice />
          <Button variant="outline" className="flex items-center justify-center gap-2" onClick={handleAddParticipant}>
            <Plus className="h-4 w-4" />
            {t('page.participants.add-another')}
          </Button>
        </div>

        <FormNavigation nextDisabled={disableNextButton} onNext={onGoNext} />
      </CardContent>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('page.participants.delete.title')}</DialogTitle>
            <DialogDescription>{t('page.participants.delete.description')}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelDelete}>
              {t('page.participants.delete.cancel')}
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              {t('page.participants.delete.confirm')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
